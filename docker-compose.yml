version: "2.3"

x-volumes:
  &default-volumes
    # Define all volumes you would like to have real-time mounted into the docker containers
    volumes:
    - .:/app:cached
x-environment:
  &default-environment
    # Route that should be used locally
    LAGOON_ROUTE: http://kalliope-local.weather.com
    # Uncomment if you like to have the system behave like in production
    #LAGOON_ENVIRONMENT_TYPE: production
  # The env var wasn't set in our environemnts which caused nginx to choke.
  # Setting it explicitly just for consistency with upstream.
    LAGOON_ENVIRONMENT_TYPE: development
    # Uncomment to enable xdebug and then restart via `docker-compose up -d`
    XDEBUG_ENABLE: "true"
    DOCKERHOST: host.docker.internal
x-user:
  &default-user
  # The default user under which the containers should run. Change this if you are on linux and run with another user than id `1000`
  user: '1000'

services:

  cli: # cli container, will be used for executing composer and any local commands (drush, drupal, etc.)
    build:
      context: .
      dockerfile: Dockerfile.cli
      args:
        GITHUB_TOKEN: "****************************************"
    image: kalliope:cli
    labels:
      # Lagoon Labels
      lagoon.type: cli-persistent
      lagoon.persistent.name: nginx # mount the persistent storage of nginx into this container
      lagoon.persistent: /app/web/sites/default/files/ # location where the persistent storage should be mounted
    << : *default-volumes # loads the defined volumes from the top
    environment:
      << : *default-environment # loads the defined environment variables from the top
      DB_HOST: postgres
      DB_USER: drupal
      DB_PASSWORD: drupal
      DB_NAME: drupal
      DB_PORT: 5432
      DB_PREFIX: ""
      DB_DRIVER: pgsql
      DB_SSL: disable
      PHP_MEMORY_LIMIT: -1
      PHP_XDEBUG: 1
      PHP_XDEBUG_DEFAULT_ENABLE: 1
      PHP_XDEBUG_REMOTE_ENABLE: 1
      PHP_XDEBUG_REMOTE_CONNECT_BACK: 0
      PHP_XDEBUG_REMOTE_HOST: "*************"
      PHP_IDE_CONFIG: "serverName=kalliope-local.weather.com"
      HASH_SALT: "pHirG0x4J0vJJxkZf53azzflFnW97EcZHItKkWSrJlA4kUjRam_XQY-obqdNsrHTC1DdcmhYsg"
      PHP_DISPLAY_ERRORS: "On"
      DSX_BASE_URL: "https://dsx-stage.weather.com"
      DSX_API_KEY: "7db9fe61-7414-47b5-9871-e17d87b8b6a0"
      DNA_ENDPOINT: "https://edit-stage.weather.com/api/content"
      S3_API_REGION: "us-east-1"
      S3_API_BUCKET: "local"
      S3_ENDPOINT: "http://s3.local:9000"
      S3_API_KEY: "minio-twc"
      S3_API_SECRET: "!all!hail!wizard!king!"
      S3_CNAME: "s3.local/local"
      # SUN API variables
      SUN_BASE_URL: "https://api.weather.com/v2/tropical"
      # This is a dummy key.
      SUN_API_KEY: "blorpblorpgalorp"
      # Instana
      INSTANA_AUTH_KEY: ""
      # Database Migration Variables
      # Set MIGRATION_DB to 1 to have Mysql database attached to Drupal
      MIGRATION_DB: 1
      MDB_HOST: mariadb
      MDB_USER: drupal
      MDB_PASSWORD: drupal
      MDB_NAME: drupal
      MDB_DRIVER: mysql
      REDIS_ENABLE: 0
      REDIS_PASSWORD: "str0ng_passw0rd"
      REDIS_HOST: "redis-sentinel:26379"
      REDIS_INSTANCE: "mymaster"
      REDIS_PREFIX: "cms-local_"
      APPLE_API_KEY: "apple-api-key"
      APPLE_API_SECRET: "apple-api-secret"
      APPLE_CHANNEL_NAME: "The Weather Channel"
      APPLE_CHANNEL_ID: "188e46eb-98f3-4a16-b84c-e1811d3bc69e"
      SAML_SP_ENTITY_ID: "kalliope.nonprod"
      SAML_IDP_ENTITY_ID: "http://www.okta.com/exk10dgz0e2bo1MNW0h8"
      SAML_IDP_SINGLE_SIGN_ON_SERVICE: "https://weather.oktapreview.com/app/weather_kalliopenonprod_1/exk10dgz0e2bo1MNW0h8/sso/saml"
      SAML_IDP_CHANGE_PASSWORD_SERVICE: "https://weather.oktapreview.com/enduser/settings"
      SAML_IDP_X509_CERTIFICATE: "-----BEGIN CERTIFICATE-----
        MIIDnjCCAoagAwIBAgIGAV2Ru1xVMA0GCSqGSIb3DQEBCwUAMIGPMQswCQYDVQQGEwJVUzETMBEG
        A1UECAwKQ2FsaWZvcm5pYTEWMBQGA1UEBwwNU2FuIEZyYW5jaXNjbzENMAsGA1UECgwET2t0YTEU
        MBIGA1UECwwLU1NPUHJvdmlkZXIxEDAOBgNVBAMMB3dlYXRoZXIxHDAaBgkqhkiG9w0BCQEWDWlu
        Zm9Ab2t0YS5jb20wHhcNMTcwNzMwMDQyMTQxWhcNMjcwNzMwMDQyMjQxWjCBjzELMAkGA1UEBhMC
        VVMxEzARBgNVBAgMCkNhbGlmb3JuaWExFjAUBgNVBAcMDVNhbiBGcmFuY2lzY28xDTALBgNVBAoM
        BE9rdGExFDASBgNVBAsMC1NTT1Byb3ZpZGVyMRAwDgYDVQQDDAd3ZWF0aGVyMRwwGgYJKoZIhvcN
        AQkBFg1pbmZvQG9rdGEuY29tMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAsRlU9aCA
        XXhpNRh5eUirrjRWmU9cnj4iRmGN3yGeXGv/WsrKzdM/XoYWf5S1Y1j44i7KEcRV/Wtdp1dHwhZq
        mgGBhfBUhSA6Y2mShbcFORbluVRf5oFDviMssDDjxl7sZOQwhMp+HFPKJbWfMLgAM0FW/BcgGKJe
        RBMWNH3pwBVIR02kn/E3uDML1KNQVlOGc+/LBXjweV/1dj8M65vq8kWe7p0IrL22i1sPSiQOEMVO
        uh1z7oHcSIOOAFsE+MJy9ClkqW0bIFQw3hnN5U0SWpiGfeQsxr8moA7MFhK8YoAVUpcTLvCCPm0S
        d6pPr9k6kcjWy9gQU1hdCaZ2i126DQIDAQABMA0GCSqGSIb3DQEBCwUAA4IBAQBoZ+K3DBEO0OSJ
        S363JcOO2+fY0V5kiPGyEMMhFbC0Kh7v8o48AMtnz4XPn+p95XSsP23DNj0mzYKXnwGFeaN6u2u0
        JF8BQkmY1+mVJfi8AiLJaIT0Om0Lw//M2NNX6ZavK7ZP7yvYv8HJ8u7kJj0NvW1UdDrbo/KTwWoZ
        nNjUFMDEj+cF5C9XcV2PCjayZQy7Ga2mt6aAeaeUIximQOglaaSsAt1si5hpByJSPNsqlPbVthDe
        QVfQLteOib4EATYaW/IWH2h9GumSISGufq7sN7hVxacbUBg5iXlUUQ7YqlInPf/nb90NTG0EEA61
        Ro6SdX7EcKMigxAHMuzrDIxD
        -----END CERTIFICATE-----"

  php:
    build:
      context: .
      dockerfile: Dockerfile.php
    extra_hosts:
      - "host.docker.internal:host-gateway"
    image: kalliope:php
    labels:
      lagoon.type: nginx-php-persistent
      lagoon.name: nginx # we want this service be part of the nginx pod in Lagoon
      lagoon.persistent: /app/web/sites/default/files/ # define where the persistent storage should be mounted too
    << : *default-volumes # loads the defined volumes from the top
    depends_on:
      - cli # basically just tells docker-compose to build the cli first
    environment:
      << : *default-environment # loads the defined environment variables from the top
      REGION: local
      GIT_REVISION: abc1234
      #UI_URL: "http://moonracer-ui-local.weather.com"
      DB_HOST: postgres
      DB_USER: drupal
      DB_PASSWORD: drupal
      DB_NAME: drupal
      DB_PORT: 5432
      DB_PREFIX: ""
      DB_SSL: disable
      DB_DRIVER: pgsql
      DSX_BASE_URL: "https://dsx-stage.weather.com"
      DSX_API_KEY: "7db9fe61-7414-47b5-9871-e17d87b8b6a0"
      PHP_MEMORY_LIMIT: 512M
      PHP_XDEBUG: 1
      PHP_XDEBUG_DEFAULT_ENABLE: 1
      PHP_XDEBUG_REMOTE_ENABLE: 1
      PHP_XDEBUG_REMOTE_CONNECT_BACK: 0
      PHP_XDEBUG_REMOTE_HOST: "*************"
      HASH_SALT: "pHirG0x4J0vJJxkZf53azzflFnW97EcZHItKkWSrJlA4kUjRam_XQY-obqdNsrHTC1DdcmhYsg"
      PHP_DISPLAY_ERRORS: "On"
      S3_API_REGION: "us-east-1"
      S3_API_BUCKET: "local"
      S3_ENDPOINT: "http://s3.local:9000"
      S3_API_KEY: "minio-twc"
      S3_API_SECRET: "!all!hail!wizard!king!"
      S3_CNAME: "s3.local/local"
      # SUN API variables
      SUN_BASE_URL: "https://api.weather.com/v2/tropical"
      # This is a dummy key
      SUN_API_KEY: "blorpblorpgalorp"
      # Instana
      INSTANA_AUTH_KEY: ""
      # Database Migration Variables
      # Set MIGRATION_DB to 1 to have Mysql database attached to Drupal
      MIGRATION_DB: 1
      MDB_HOST: mariadb
      MDB_USER: drupal
      MDB_PASSWORD: drupal
      MDB_NAME: drupal
      MDB_DRIVER: mysql
      REDIS_ENABLE: 0
      REDIS_PASSWORD: "str0ng_passw0rd"
      REDIS_HOST: "redis-sentinel:26379"
      REDIS_INSTANCE: "mymaster"
      REDIS_PREFIX: "cms-local_"
      APPLE_API_KEY: "apple-api-key"
      APPLE_API_SECRET: "apple-api-secret"
      APPLE_CHANNEL_NAME: "The Weather Channel"
      APPLE_CHANNEL_ID: "188e46eb-98f3-4a16-b84c-e1811d3bc69e"
      # PHP EXECUTION VARIABLES
      # PHP EXECUTION VARIABLE DEFAULTS
      #PHP_MAX_EXECUTION_TIME: 900
      #PHP_MAX_INPUT_VARS: 1000
      #PHP_DISPLAY_STARTUP_ERRORS: 'Off'
      #PHP_APC_SHM_SIZE: '32m'
      #PHP_APC_ENABLED: 1
      #PHP_FPM_PM_MAX_CHILDREN: 50
      #PHP_FPM_PM_START_SERVERS: 2
      #PHP_FPM_PM_MIN_SPARE_SERVERS: 2
      #PHP_FPM_PM_MAX_SPARE_SERVERS: 2
      #PHP_FPM_PM_PROCESS_IDLE_TIMEOUT: '60s'
      #PHP_FPM_PM_MAX_REQUESTS: 500


  nginx:
    build:
      context: .
      dockerfile: Dockerfile.nginx
    extra_hosts:
      - "host.docker.internal:host-gateway"
    image: kalliope:nginx
    labels:
      lagoon.type: nginx-php-persistent
      lagoon.persistent: /app/web/sites/default/files/ # define where the persistent storage should be mounted too
      traefik.backend: nginx
      traefik.port: '8080'
      traefik.frontend.rule: 'Host:kalliope-local.weather.com'
    << : *default-volumes # loads the defined volumes from the top
    depends_on:
    - cli # basically just tells docker-compose to build the cli first
    environment:
      << : *default-environment # loads the defined environment variables from the top
      LAGOON_LOCALDEV_URL: kalliope-local.weather.com # generate another route for nginx, by default we go to varnish
    ports:
    - '8888:8080'

  mariadb:
    image: wodby/mariadb:10.2-3.0.2
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: drupal
      MYSQL_USER: drupal
      MYSQL_PASSWORD: drupal
    ports:
      - '3306:3306'
    volumes:
      - ./mariadb-init:/docker-entrypoint-initdb.d # Place init .sql file(s) here.
    # - /path/to/mariadb/data/on/host:/var/lib/mysql # I want to manage volumes manually.

  postgres:
    image: wodby/postgres:12-1.23.3
    environment:
      POSTGRES_PASSWORD: drupal
      POSTGRES_DB: drupal
      POSTGRES_USER: drupal
    labels:
      traefik.backend: postgres
      traefik.port: '5432'
      traefik.frontend.rule: 'Host:kalliope-local.weather.com'
    ports:
      - '5432:5432'
    volumes:
      - ./postgres-init:/docker-entrypoint-initdb.d # Place init file(s) here.
      #- /path/to/postgres/data/on/host:/var/lib/postgresql/data # I want to manage volumes manually.

  # redis:
  #   image: 'bitnami/redis:latest'
  #   environment:
  #     - REDIS_REPLICATION_MODE=master
  #     - REDIS_PASSWORD=str0ng_passw0rd
  # redis-slave:
  #   image: 'bitnami/redis:latest'
  #   environment:
  #     - REDIS_REPLICATION_MODE=slave
  #     - REDIS_MASTER_HOST=redis
  #     - REDIS_MASTER_PASSWORD=str0ng_passw0rd
  #     - REDIS_PASSWORD=str0ng_passw0rd
  #   depends_on:
  #     - redis
  # redis-sentinel:
  #   image: 'bitnami/redis-sentinel:latest'
  #   environment:
  #     - REDIS_MASTER_PASSWORD=str0ng_passw0rd
  #   depends_on:
  #     - redis
  #     - redis-slave

  minio:
    image: minio/minio
    ports:
      - "9001:9000"
    volumes:
      - ./files/data:/data
      - ./files/config:/root/.minio
    environment:
      MINIO_ACCESS_KEY: "minio-twc"
      MINIO_SECRET_KEY: "!all!hail!wizard!king!"
    networks:
        default:
          aliases:
            - s3.local
    labels:
      traefik.backend: minio
      traefik.port: '9000'
      traefik.frontend.rule: 'Host:s3.local'
    command: server /data

  minioinitclient:
    image: minio/mc
    depends_on:
      - minio
    entrypoint: >
      /bin/sh -c "
      /usr/bin/mc config host add myminio http://minio:9000 minio-twc !all!hail!wizard!king!;
      /usr/bin/mc rm -r --force myminio/local;
      /usr/bin/mc mb myminio/local;
      /usr/bin/mc policy public myminio/local;
      exit 0;
      "

  traefik:
    image: traefik:v1.7.16
    command: -c /dev/null --web --docker --logLevel=INFO
    ports:
      - '80:80'
      - '8080:8080' # Dashboard
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock

networks:
  default:
    name: kalliope-net

volumes:
  data1:
