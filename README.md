# cdel-cms-nextgen

Next Generation CMS for Weather.com

## Setup

- `<NAME_EMAIL>:TheWeatherCompany/cdel-cms-nextgen.git`
- `cd cdel-cms-nextgen`
- Add hostname to your hosts file

  - 'sudo nano /etc/hosts`
  - Add `127.0.0.1 kalliope-local.weather.com`
  - Add `127.0.0.1 s3.local`

- Place a Postgres dump in `postgres-init` (see [Install from Database Backup](#install-from-database-backup), below)
- `./build-base-images.sh`
- `docker compose up --build`
- Install Composer dependencies using `docker compose exec cli composer install`
  - Current build requires Composer 1. The upgrade to Drupal 9 will use Composer 2, which will enable local building
- Build the JS Library (including Slate Editor and Daybreak Dashboard) using the following code from within CLI:
```$bash
yarn global add lerna@^6 && \
        lerna bootstrap && \
        yarn run build:production && \
        yarn run copy-assets && \
        rm -rf node_modules
```
- Install Drupal using one of the options below - [Install Drupal from Scratch without a Database](#install-drupal-from-scratch-without-a-database) or [Install from a Database Backup](#install-from-database-backup).

### Install from Database Backup

- Before running `docker compose up`, place Postgres dump in `.sql` or `.sql.gz` formats into the `postgres-init` directory
- Omit the `nextgen:build` step, as the database will be initialized on build from the dump file.

### Refresh from Database Backup

- Replace any existing database backup in `postgres-init\` with your new backup file (ensure there is only one `.sql` or `.sql.gz` file in the directory)
- If your stack is currently running, execute `docker compose down` - this will remove the current statefulness of the running containers
- Bring your stack back up with `docker compose up` and the new database will be ingested on startup by postgres.

### Install Drupal from scratch without a database

- `docker compose exec cli drupal nextgen:build`

### Initial Login

- Login with your configured user information from the database
  - Alternately, passwords can be reset using Drupal Console on the CLI Container
    - `docker compose exec cli drupal uii <your-user-id>' for a one-time reset link -or-
    - 'docker compose exec cli drupal upr' to directly reset a user password
- DB Credentials are configured in `docker-compose.yml` are found below:
  - Type: `PostgreSQL`
  - User: `drupal8`
  - Pass: `drupal8`
  - Database: `drupal8`
  - Host: `database`
  - Port: `5432`
- Minio (Local s3 Service):
  - Access via http://s3.local
  - Key: `minio-twc`
  - Secret: `!all!hail!wizard!king!`

## MariaDB and DNA Databases

The Mariadb MySQL instance for Data migrations works identically to postgres - dumps in the `mariadb-init` directory will be ingested on startup. Remember to enable `migrate_db` in docker-compose.yml and provide credentials. These credentials will need to be added to a kube secret file on non-local environments.

## Contrib

This repo follows `git-flow-avh` branching patterns. In order to get setup for contributing to this project, please see the following link for instructions on installing `git-flow-avh` command line helper. [Git Flow Cheatsheet](http://danielkummer.github.io/git-flow-cheatsheet/)

### Jira Project Tracking

A ticket should be created under the `Content Delivery CDEL Project` and should reference the component you wish to create the feature or bug for. In this case it is `Kalliope`.
[CDEL Jira Project](https://jira.weather.com:8443/secure/RapidBoard.jspa?rapidView=22&projectKey=CDEL)

### Branch Naming

We prefer to use a semantic naming strategy for branch names, using the following pattern `{type}/{author}/{jira-ticket}-{branch-name-seperated-with-hypens}`. e.g *feature/swallace/CDEL-0000-this-this-my-cool-new-feature*

### Creating a Feature

- Create a Jira Ticket **See Jira Project Tracking**
- Create a feature branch based off of the develop branch `git flow feature start **See Branch Naming**`
- When the feature is complete, Create a PR from the feature branch so that it can be tested and mark the ticket as `Dev Complete` so that it triggers QA to pick it up.
- Once the ticket is considered `Testing Passed`, run `git flow feature finish {your-feature-branch-name}`. This will close your feature and merge it into the develop branch.

### Creating a Hotfix

**Please Note!: Hotfix should only be created when there is an emergency bug with production. For anything that isnt critical please see `Creating a Feature`**

- Create a Jira Ticket **See Jira Project Tracking**
- Create a hotfix based on the main branch. `git flow hotfix **VERSION** **See Branch Naming**`.
- When the hotfix is complete, Create a PR from the hotfix branch so that it can be tested and mark the ticket as `Dev Complete` so that it triggers QA to pick it up.
- Once the ticket is considered `Testing Passed`, run `git flow hotfix finish **VERSION**`. This will close your hotfix and merge directly into main and develop branches.

### Deployment Strategy

Below is a list of what branches will be found on what environments.
| Environment | Branch |
| ----------- | ----------- |
| Dev | Feature/Bugfix |
| Stage | Release |
| QAT | Develop |
| Prod | Main |

In the future we hope to have an ephemeral enviroment and automated deployments to these environements. For now it will require manual releases. Please ask `#web-dev-devops` slack channel for assistance with Jenkins deployments.

## Testing

The repo has been configured with a customized version of Drupal's phpunit.xml file, which is available in the root of the project. Testing via the cli using this config can be accomplished by running the following inside the php container:

```$bash
/app/vendor/bin/phpunit -c /app/phpunit.xml <path/to/test/you/wish/to_run>
```

When setting up PHPStorm for integrated testing, you should configure the testrunner by going to `Preferences->Languages & Frameworks->PHP->Test Frameworks` and ensuring that your interpreter's `Default Configuration File` field is enabled and set to `/app/phpunit.xml`.
