<?php

namespace Drupal\ccs_connector\Form;

use <PERSON>upal\Core\Form\ConfigFormBase;
use <PERSON>upal\Core\Form\FormStateInterface;

/**
 * Configuration form for revalidation settings.
 */
class PayloadRevalidationSettingsForm extends ConfigFormBase {

  /**
   * {@inheritdoc}
   */
  public function getFormId() {
    return 'ccs_connector_revalidation_settings';
  }

  /**
   * {@inheritdoc}
   */
  protected function getEditableConfigNames() {
    return ['ccs_connector.revalidation_settings'];
  }

  /**
   * {@inheritdoc}
   */
  public function buildForm(array $form, FormStateInterface $form_state) {
    $config = $this->config('ccs_connector.revalidation_settings');
    $form['revalidation_settings'] = [
      '#type' => 'details',
      '#title' => $this->t('Revalidation API Settings'),
      '#open' => TRUE,
    ];

    $form['revalidation_settings']['path_api_base_url'] = [
      '#type' => 'url',
      '#title' => $this->t('Path Revalidation API Base URL'),
      '#default_value' => $config->get('path_api_base_url') ?: 'https://wxnext.weather.com/api/revalidate',
      '#description' => $this->t('The base URL for the revalidation API (e.g., https://wxnext.weather.com/api/revalidate).'),
      '#required' => TRUE,
    ];

    $form['revalidation_settings']['tag_api_base_url'] = [
      '#type' => 'url',
      '#title' => $this->t('Tag Revalidation API Base URL'),
      '#default_value' => $config->get('tag_api_base_url') ?: 'https://wxnext.weather.com/api/revalidate-tag',
      '#description' => $this->t('The base URL for the revalidation API (e.g., https://wxnext.weather.com/api/revalidate-tag).'),
      '#required' => TRUE,
    ];

    $form['revalidation_settings']['timeout'] = [
      '#type' => 'number',
      '#title' => $this->t('Request Timeout (seconds)'),
      '#default_value' => $config->get('timeout') ?: 10,
      '#min' => 1,
      '#max' => 60,
      '#description' => $this->t('The timeout in seconds for revalidation API requests.'),
      '#required' => TRUE,
    ];

    $form['revalidation_settings']['connect_timeout'] = [
      '#type' => 'number',
      '#title' => $this->t('Connection Timeout (seconds)'),
      '#default_value' => $config->get('connect_timeout') ?: 5,
      '#min' => 1,
      '#max' => 30,
      '#description' => $this->t('The connection timeout in seconds for revalidation API requests.'),
      '#required' => TRUE,
    ];

    return parent::buildForm($form, $form_state);
  }

  /**
   * {@inheritdoc}
   */
  public function validateForm(array &$form, FormStateInterface $form_state) {
    parent::validateForm($form, $form_state);

    // Validate the URL
    $url = $form_state->getValue('path_api_base_url');
    if (!filter_var($url, FILTER_VALIDATE_URL)) {
      $form_state->setErrorByName('revalidation_settings][path_api_base_url', $this->t('Please enter a valid URL.'));
    }

    $url = $form_state->getValue('tag_api_base_url');
    if (!filter_var($url, FILTER_VALIDATE_URL)) {
      $form_state->setErrorByName('revalidation_settings][tag_api_base_url', $this->t('Please enter a valid URL.'));
    }
  }

  /**
   * {@inheritdoc}
   */
  public function submitForm(array &$form, FormStateInterface $form_state) {
    $config = $this->config('ccs_connector.revalidation_settings');
    $values = $form_state->getValues();
    $config->set('path_api_base_url', $values['path_api_base_url']);
    $config->set('tag_api_base_url', $values['tag_api_base_url']);
    $config->set('timeout', $values['timeout']);
    $config->set('connect_timeout', $values['connect_timeout']);
    $config->save();
    parent::submitForm($form, $form_state);
  }
}