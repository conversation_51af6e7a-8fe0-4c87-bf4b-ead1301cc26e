{"root": true, "env": {"browser": true, "es6": true}, "globals": {"ENV": true}, "extends": "airbnb", "parserOptions": {"ecmaFeatures": {"jsx": true}, "ecmaVersion": 2017, "sourceType": "module"}, "plugins": ["import", "react"], "rules": {"camelcase": [0, {"properties": "never"}], "indent": ["error", 2, {"SwitchCase": 1}], "linebreak-style": ["error", "unix"], "quotes": ["error", "single"], "semi": ["error", "always"], "import/no-extraneous-dependencies": ["error", {"devDependencies": true}], "react/jsx-filename-extension": [1, {"extensions": [".js", ".jsx"]}], "import/no-unresolved": [2, {"ignore": ["drupal", "drupalSettings"]}]}}